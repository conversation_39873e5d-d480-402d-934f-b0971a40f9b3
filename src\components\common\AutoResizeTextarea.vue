<script setup lang="ts">
import {
  ref,
  onMounted,
  watch,
  nextTick,
  defineProps,
  defineModel,
  withDefaults,
} from "vue";

interface AutoResizeTextareaProps {
  placeholder?: string;
  disabled?: boolean;
  minHeight?: number;
  maxHeight?: number;
  focusSave?: boolean;
  saveCallBack?: () => void;
}

const props = withDefaults(defineProps<AutoResizeTextareaProps>(), {
  placeholder: "请输入",
  disabled: false,
  minHeight: 40,
  maxHeight: 300,
  focusSave: false,
});

const model = defineModel<string>();
const textareaRef = ref<HTMLTextAreaElement>();

// 自动调整高度的函数
const adjustHeight = async () => {
  if (!textareaRef.value) return;

  await nextTick();

  const textarea = textareaRef.value;
  const minHeight = props.minHeight;
  const maxHeight = props.maxHeight;

  // 重置高度为auto以获取准确的scrollHeight
  textarea.style.height = "auto";

  // 获取内容的实际高度
  let scrollHeight = textarea.scrollHeight;

  // 确保scrollHeight不小于minHeight
  if (scrollHeight < minHeight) {
    scrollHeight = minHeight;
  }

  let targetHeight = minHeight;

  if (scrollHeight <= maxHeight) {
    // 内容高度在范围内，使用实际高度
    targetHeight = scrollHeight;
  } else {
    // 内容超过最大高度，使用最大高度
    targetHeight = maxHeight;
  }

  // 设置最终高度
  textarea.style.height = `${targetHeight}px`;

  // 设置滚动条显示
  if (targetHeight >= maxHeight && textarea.scrollHeight > maxHeight) {
    textarea.style.overflowY = "auto";
  } else {
    textarea.style.overflowY = "hidden";
  }
};

// 监听内容变化
watch(
  () => model.value,
  () => {
    adjustHeight();
  },
  { immediate: true }
);

// 处理输入事件
const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  model.value = target.value;
  adjustHeight();
};

// 组件挂载后初始化高度
onMounted(() => {
  // 确保DOM完全渲染后再调整高度
  nextTick(() => {
    if (textareaRef.value) {
      // 先设置初始高度
      textareaRef.value.style.height = `${props.minHeight}px`;
      // 然后调整高度
      adjustHeight();
    }
  });
});

// 暴露方法供父组件调用（保持与InlineEditor接口兼容）
const getData = (): string => {
  return model.value || "";
};

const setData = (value: string) => {
  model.value = value;
  nextTick(() => {
    adjustHeight();
  });
};

defineExpose({
  getData,
  setData,
});
</script>

<template>
  <textarea
    ref="textareaRef"
    v-model="model"
    :placeholder="placeholder"
    :disabled="disabled"
    @input="handleInput"
    class="auto-resize-textarea"
  />
</template>

<style scoped>
.auto-resize-textarea {
  width: 100%;
  height: v-bind('props.minHeight + "px"'); /* 设置初始高度 */
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.5;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
  overflow-y: hidden; /* 默认隐藏滚动条 */
}

.auto-resize-textarea:focus {
  border-color: #409eff;
}

.auto-resize-textarea:disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.auto-resize-textarea::placeholder {
  color: #c0c4cc;
}
</style>
